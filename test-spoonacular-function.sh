#!/bin/bash

# Test script for the search_spoonacular_recipes cloud function
# This script tests both local emulator and deployed function

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="chefpal-a9abe"
EMULATOR_URL="http://localhost:5001/${PROJECT_ID}/us-central1/search_spoonacular_recipes"
DEPLOYED_URL="https://us-central1-${PROJECT_ID}.cloudfunctions.net/search_spoonacular_recipes"

# Test data
TEST_PAYLOAD='{
  "searchQueries": ["pasta", "chicken curry"],
  "diet": "vegetarian",
  "intolerances": "gluten,dairy",
  "maxResults": 2
}'

# Function to get Firebase auth token (you need to implement this)
get_auth_token() {
    echo "To get an auth token, you can:"
    echo "1. Use Firebase CLI: firebase auth:print-access-token"
    echo "2. Or get it from your app's authentication"
    echo "3. For testing, you can create a test user and get their ID token"
    echo ""
    echo "Please enter your Firebase ID token:"
    read -r TOKEN
    echo "$TOKEN"
}

# Function to test the cloud function
test_function() {
    local url=$1
    local description=$2
    
    echo -e "${YELLOW}Testing $description...${NC}"
    echo "URL: $url"
    echo "Payload: $TEST_PAYLOAD"
    echo ""
    
    # Get auth token
    AUTH_TOKEN=$(get_auth_token)
    
    if [ -z "$AUTH_TOKEN" ]; then
        echo -e "${RED}No auth token provided. Skipping test.${NC}"
        return 1
    fi
    
    # Make the request
    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -d "$TEST_PAYLOAD" \
        "$url")
    
    # Extract HTTP status and body
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    echo "HTTP Status: $http_status"
    echo "Response:"
    echo "$body" | jq . 2>/dev/null || echo "$body"
    echo ""
    
    if [ "$http_status" -eq 200 ]; then
        echo -e "${GREEN}✅ Test passed!${NC}"
    else
        echo -e "${RED}❌ Test failed with status $http_status${NC}"
    fi
    echo "----------------------------------------"
}

# Function to test without authentication (should fail)
test_unauthorized() {
    local url=$1
    
    echo -e "${YELLOW}Testing unauthorized access (should fail)...${NC}"
    
    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$TEST_PAYLOAD" \
        "$url")
    
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    echo "HTTP Status: $http_status"
    echo "Response: $body"
    
    if [ "$http_status" -eq 401 ]; then
        echo -e "${GREEN}✅ Unauthorized test passed (correctly rejected)!${NC}"
    else
        echo -e "${RED}❌ Unauthorized test failed - should have returned 401${NC}"
    fi
    echo "----------------------------------------"
}

# Main execution
echo -e "${GREEN}🧪 Testing Spoonacular Cloud Function${NC}"
echo "========================================"

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}Warning: jq is not installed. JSON responses won't be formatted.${NC}"
    echo "Install with: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    echo ""
fi

# Test menu
echo "Choose test option:"
echo "1. Test local emulator"
echo "2. Test deployed function"
echo "3. Test both"
echo "4. Test unauthorized access"
echo "5. Exit"
echo ""
read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo "Make sure Firebase emulator is running: firebase emulators:start"
        test_function "$EMULATOR_URL" "Local Emulator"
        ;;
    2)
        test_function "$DEPLOYED_URL" "Deployed Function"
        ;;
    3)
        echo "Make sure Firebase emulator is running: firebase emulators:start"
        test_function "$EMULATOR_URL" "Local Emulator"
        test_function "$DEPLOYED_URL" "Deployed Function"
        ;;
    4)
        echo "Choose URL for unauthorized test:"
        echo "1. Local emulator"
        echo "2. Deployed function"
        read -p "Enter choice (1-2): " auth_choice
        if [ "$auth_choice" -eq 1 ]; then
            test_unauthorized "$EMULATOR_URL"
        else
            test_unauthorized "$DEPLOYED_URL"
        fi
        ;;
    5)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo "Invalid choice"
        exit 1
        ;;
esac
