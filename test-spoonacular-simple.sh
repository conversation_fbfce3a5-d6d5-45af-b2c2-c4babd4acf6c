#!/bin/bash

# Simple test script for Spoonacular cloud function
# Usage: ./test-spoonacular-simple.sh [local|deployed] [auth_token]

set -e

# Configuration
PROJECT_ID="chefpal-a9abe"
EMULATOR_URL="http://localhost:5001/${PROJECT_ID}/us-central1/search_spoonacular_recipes"
DEPLOYED_URL="https://us-central1-${PROJECT_ID}.cloudfunctions.net/search_spoonacular_recipes"

# Determine URL
if [ "$1" = "local" ]; then
    URL="$EMULATOR_URL"
    echo "Testing LOCAL emulator..."
    echo "Make sure emulator is running: firebase emulators:start"
elif [ "$1" = "deployed" ]; then
    URL="$DEPLOYED_URL"
    echo "Testing DEPLOYED function..."
else
    echo "Usage: $0 [local|deployed] [auth_token]"
    echo "Example: $0 local eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
    exit 1
fi

# Auth token
AUTH_TOKEN="$2"
if [ -z "$AUTH_TOKEN" ]; then
    echo "Error: Please provide an auth token as the second argument"
    echo "Get one with: firebase auth:print-access-token"
    exit 1
fi

# Test payload
TEST_PAYLOAD='{
  "searchQueries": ["pasta", "chicken curry"],
  "diet": "vegetarian",
  "intolerances": "gluten,dairy",
  "maxResults": 2
}'

echo "URL: $URL"
echo "Payload: $TEST_PAYLOAD"
echo ""

# Make the request
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -d "$TEST_PAYLOAD" \
  "$URL" | jq . || echo "Response received (install jq for formatting)"
