# Testing the Spoonacular Cloud Function

This guide shows you how to test the `search_spoonacular_recipes` cloud function from the command line.

## Prerequisites

1. **Install required tools:**
   ```bash
   # Install jq for JSON formatting (optional but recommended)
   brew install jq  # macOS
   # or
   sudo apt-get install jq  # Ubuntu/Debian
   ```

2. **Set up Firebase CLI:**
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

3. **Set up Spoonacular API key:**
   ```bash
   # Add the Spoonacular API key to Firebase secrets
   firebase functions:secrets:set SPOONACULAR_API_KEY
   # Enter your Spoonacular API key when prompted
   ```

## Testing Options

### Option 1: Test with Firebase Emulator (Recommended for Development)

1. **Start the Firebase emulator:**
   ```bash
   firebase emulators:start
   ```

2. **Get an authentication token:**
   ```bash
   # Method 1: Use Firebase CLI
   firebase auth:print-access-token
   
   # Method 2: Create a test user in your app and get their ID token
   # (You'll need to implement this in your app)
   ```

3. **Test the function:**
   ```bash
   # Replace YOUR_AUTH_TOKEN with the actual token
   curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
     -d '{
       "searchQueries": ["pasta", "chicken curry"],
       "diet": "vegetarian",
       "intolerances": "gluten,dairy",
       "maxResults": 2
     }' \
     "http://localhost:5001/chefpal-a9abe/us-central1/search_spoonacular_recipes" | jq .
   ```

### Option 2: Test Deployed Function

1. **Deploy the function:**
   ```bash
   firebase deploy --only functions
   ```

2. **Test the deployed function:**
   ```bash
   # Replace YOUR_AUTH_TOKEN with the actual token
   curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
     -d '{
       "searchQueries": ["pasta", "chicken curry"],
       "diet": "vegetarian",
       "intolerances": "gluten,dairy",
       "maxResults": 2
     }' \
     "https://us-central1-chefpal-a9abe.cloudfunctions.net/search_spoonacular_recipes" | jq .
   ```

### Option 3: Use the Test Scripts

1. **Use the simple test script:**
   ```bash
   # For local testing
   ./test-spoonacular-simple.sh local YOUR_AUTH_TOKEN
   
   # For deployed function testing
   ./test-spoonacular-simple.sh deployed YOUR_AUTH_TOKEN
   ```

2. **Use the interactive test script:**
   ```bash
   ./test-spoonacular-function.sh
   ```

## Test Cases

### 1. Basic Test
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "searchQueries": ["pasta"]
  }' \
  "YOUR_FUNCTION_URL"
```

### 2. Test with Diet Preferences
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "searchQueries": ["chicken", "beef"],
    "diet": "ketogenic"
  }' \
  "YOUR_FUNCTION_URL"
```

### 3. Test with Intolerances
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "searchQueries": ["bread", "pasta"],
    "intolerances": "gluten"
  }' \
  "YOUR_FUNCTION_URL"
```

### 4. Test with All Parameters
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{
    "searchQueries": ["salad", "soup", "smoothie"],
    "diet": "vegan",
    "intolerances": "dairy,eggs",
    "maxResults": 5
  }' \
  "YOUR_FUNCTION_URL"
```

### 5. Test Error Cases

**Missing authentication:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"searchQueries": ["pasta"]}' \
  "YOUR_FUNCTION_URL"
# Should return 401 Unauthorized
```

**Missing search queries:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{}' \
  "YOUR_FUNCTION_URL"
# Should return 400 Bad Request
```

**Wrong HTTP method:**
```bash
curl -X GET \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  "YOUR_FUNCTION_URL"
# Should return 405 Method Not Allowed
```

## Expected Response Format

**Success Response (200):**
```json
{
  "recipes": [
    {
      "id": 123456,
      "title": "Vegetarian Pasta Primavera",
      "image": "https://spoonacular.com/recipeImages/123456-556x370.jpg",
      "readyInMinutes": 30,
      "servings": 4,
      "sourceUrl": "https://example.com/recipe",
      "searchQuery": "pasta",
      "ingredients": [
        {
          "name": "pasta",
          "amount": 200,
          "unit": "g",
          "original": "200g pasta"
        }
      ],
      "instructions": "1. Boil water in a large pot...",
      "summary": "A delicious vegetarian pasta dish..."
    }
  ],
  "totalFound": 1
}
```

**Error Response:**
```json
{
  "error": "Missing or invalid 'searchQueries' array"
}
```

## Troubleshooting

1. **401 Unauthorized:** Check your Firebase ID token
2. **500 Internal Server Error:** Check if SPOONACULAR_API_KEY is set correctly
3. **Function not found:** Make sure the function is deployed
4. **Emulator not responding:** Ensure `firebase emulators:start` is running

## Getting Firebase ID Token for Testing

### Method 1: From Firebase CLI
```bash
firebase auth:print-access-token
```

### Method 2: From Your App (for real testing)
Add this to your app temporarily to get a token:
```javascript
import { auth } from '@/firebase/firebaseConfig';

// After user is authenticated
const user = auth.currentUser;
if (user) {
  const token = await user.getIdToken();
  console.log('ID Token:', token);
}
```

### Method 3: Create a Test User
You can create a test user in Firebase Console and use their credentials to get a token.

## Performance Testing

For load testing, you can use tools like:
- **Apache Bench (ab):** `ab -n 100 -c 10 -H "Authorization: Bearer TOKEN" -T "application/json" -p payload.json URL`
- **wrk:** More advanced HTTP benchmarking tool
- **Artillery:** For complex load testing scenarios
