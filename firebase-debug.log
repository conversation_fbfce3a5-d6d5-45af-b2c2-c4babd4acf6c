[debug] [2025-06-02T01:49:20.257Z] ----------------------------------------------------------------------
[debug] [2025-06-02T01:49:20.259Z] Command:       /Users/<USER>/.nvm/versions/node/v23.9.0/bin/node /Users/<USER>/.nvm/versions/node/v23.9.0/bin/firebase emulators:start
[debug] [2025-06-02T01:49:20.259Z] CLI Version:   14.5.1
[debug] [2025-06-02T01:49:20.259Z] Platform:      darwin
[debug] [2025-06-02T01:49:20.259Z] Node Version:  v23.9.0
[debug] [2025-06-02T01:49:20.260Z] Time:          Sun Jun 01 2025 20:49:20 GMT-0500 (Central Daylight Time)
[debug] [2025-06-02T01:49:20.260Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-02T01:49:20.375Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-02T01:49:20.375Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: auth, functions, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, extensions"}}
[debug] [2025-06-02T01:49:20.377Z] Checked if tokens are valid: true, expires at: 1748831373024
[debug] [2025-06-02T01:49:20.377Z] Checked if tokens are valid: true, expires at: 1748831373024
[debug] [2025-06-02T01:49:20.377Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudresourcemanager.googleapis.com [none]
[debug] [2025-06-02T01:49:20.377Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudresourcemanager.googleapis.com x-goog-quota-user=projects/chefpal-a9abe
[debug] [2025-06-02T01:49:20.712Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudresourcemanager.googleapis.com 200
[debug] [2025-06-02T01:49:20.712Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/chefpal-a9abe/services/cloudresourcemanager.googleapis.com [omitted]
[debug] [2025-06-02T01:49:20.712Z] Checked if tokens are valid: true, expires at: 1748831373024
[debug] [2025-06-02T01:49:20.712Z] Checked if tokens are valid: true, expires at: 1748831373024
[debug] [2025-06-02T01:49:20.712Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe [none]
[debug] [2025-06-02T01:49:20.935Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe 200
[debug] [2025-06-02T01:49:20.935Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/chefpal-a9abe {"projectNumber":"116153101016","projectId":"chefpal-a9abe","lifecycleState":"ACTIVE","name":"chefpal","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-03-21T02:56:02.438112Z"}
[debug] [2025-06-02T01:49:21.281Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T01:49:21.281Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T01:49:21.281Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-06-02T01:49:21.284Z] [hub] writing locator at /var/folders/7s/hxvvlfcs47qcml7v54z__k6m0000gn/T/hub-chefpal-a9abe.json
[debug] [2025-06-02T01:49:21.287Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-06-02T01:49:21.544Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T01:49:21.544Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T01:49:21.544Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-06-02T01:49:21.544Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":9099}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-06-02T01:49:21.558Z] defaultcredentials: writing to file /Users/<USER>/.config/firebase/haoyang_zona_gmail.com_application_default_credentials.json
[debug] [2025-06-02T01:49:21.560Z] Setting GAC to /Users/<USER>/.config/firebase/haoyang_zona_gmail.com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to /Users/<USER>/.config/firebase/haoyang_zona_gmail.com_application_default_credentials.json"}}
[debug] [2025-06-02T01:49:21.560Z] Checked if tokens are valid: true, expires at: 1748831373024
[debug] [2025-06-02T01:49:21.560Z] Checked if tokens are valid: true, expires at: 1748831373024
[debug] [2025-06-02T01:49:21.560Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/chefpal-a9abe/adminSdkConfig [none]
