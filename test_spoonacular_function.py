#!/usr/bin/env python3
"""
Test script for the Spoonacular cloud function
Usage: python test_spoonacular_function.py [local|deployed] [auth_token]
"""

import sys
import json
import requests
from typing import Dict, Any, Optional

# Configuration
PROJECT_ID = "chefpal-a9abe"
EMULATOR_URL = f"http://localhost:5001/{PROJECT_ID}/us-central1/search_spoonacular_recipes"
DEPLOYED_URL = f"https://us-central1-{PROJECT_ID}.cloudfunctions.net/search_spoonacular_recipes"

def test_function(url: str, auth_token: str, payload: Dict[str, Any]) -> None:
    """Test the cloud function with given parameters."""
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {auth_token}"
    }
    
    print(f"🧪 Testing URL: {url}")
    print(f"📦 Payload: {json.dumps(payload, indent=2)}")
    print("=" * 50)
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"⏱️  Response Time: {response.elapsed.total_seconds():.2f}s")
        
        if response.status_code == 200:
            print("✅ Success!")
            data = response.json()
            print(f"📈 Total recipes found: {data.get('totalFound', 0)}")
            
            if data.get('recipes'):
                print("\n🍽️  Recipe titles:")
                for i, recipe in enumerate(data['recipes'][:3], 1):  # Show first 3
                    print(f"  {i}. {recipe.get('title', 'Unknown')}")
                    print(f"     ⏰ {recipe.get('readyInMinutes', 'Unknown')} minutes")
                    print(f"     🥄 {len(recipe.get('ingredients', []))} ingredients")
        else:
            print("❌ Error!")
            try:
                error_data = response.json()
                print(f"💬 Error message: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"💬 Raw response: {response.text}")
                
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - is the emulator running?")
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
    
    print("=" * 50)

def run_test_suite(url: str, auth_token: str) -> None:
    """Run a comprehensive test suite."""
    
    test_cases = [
        {
            "name": "Basic Test",
            "payload": {
                "searchQueries": ["pasta"]
            }
        },
        {
            "name": "Multiple Queries",
            "payload": {
                "searchQueries": ["pasta", "chicken curry", "salad"]
            }
        },
        {
            "name": "With Diet Preference",
            "payload": {
                "searchQueries": ["chicken", "beef"],
                "diet": "ketogenic"
            }
        },
        {
            "name": "With Intolerances",
            "payload": {
                "searchQueries": ["bread", "pasta"],
                "intolerances": "gluten"
            }
        },
        {
            "name": "All Parameters",
            "payload": {
                "searchQueries": ["salad", "soup"],
                "diet": "vegan",
                "intolerances": "dairy,eggs",
                "maxResults": 3
            }
        }
    ]
    
    print("🚀 Running comprehensive test suite...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        test_function(url, auth_token, test_case['payload'])

def test_error_cases(url: str, auth_token: str) -> None:
    """Test error handling."""
    
    print("🔍 Testing error cases...")
    print("=" * 50)
    
    # Test without auth token
    print("\n🚫 Testing without authentication:")
    try:
        response = requests.post(url, json={"searchQueries": ["pasta"]}, timeout=10)
        print(f"Status: {response.status_code} (should be 401)")
        if response.status_code == 401:
            print("✅ Correctly rejected unauthorized request")
        else:
            print("❌ Should have returned 401")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test with empty payload
    print("\n📭 Testing with empty payload:")
    test_function(url, auth_token, {})
    
    # Test with invalid search queries
    print("\n🔤 Testing with invalid search queries:")
    test_function(url, auth_token, {"searchQueries": "not an array"})

def main():
    """Main function."""
    
    if len(sys.argv) < 3:
        print("Usage: python test_spoonacular_function.py [local|deployed] [auth_token] [test_type]")
        print("test_type options: basic, suite, errors, all (default: basic)")
        print("\nExample:")
        print("  python test_spoonacular_function.py local eyJhbGciOiJSUzI1NiIs...")
        print("  python test_spoonacular_function.py deployed TOKEN suite")
        sys.exit(1)
    
    environment = sys.argv[1]
    auth_token = sys.argv[2]
    test_type = sys.argv[3] if len(sys.argv) > 3 else "basic"
    
    if environment == "local":
        url = EMULATOR_URL
        print("🏠 Testing LOCAL emulator")
        print("Make sure emulator is running: firebase emulators:start")
    elif environment == "deployed":
        url = DEPLOYED_URL
        print("☁️  Testing DEPLOYED function")
    else:
        print("Error: First argument must be 'local' or 'deployed'")
        sys.exit(1)
    
    print(f"🔑 Using auth token: {auth_token[:20]}...")
    print()
    
    if test_type == "basic":
        test_function(url, auth_token, {"searchQueries": ["pasta", "chicken curry"]})
    elif test_type == "suite":
        run_test_suite(url, auth_token)
    elif test_type == "errors":
        test_error_cases(url, auth_token)
    elif test_type == "all":
        run_test_suite(url, auth_token)
        test_error_cases(url, auth_token)
    else:
        print(f"Unknown test type: {test_type}")
        sys.exit(1)

if __name__ == "__main__":
    main()
